Create it in raw python with structured directories and structured files and structured components - without using any agantic frameworks like pydantic ai or langchain


Create and develop a modern and powerful CLI terminal system powered by AI with the LLMs Function Tools calling and executing capabilities and the system capture tools outputs(Do not show the tools outputs in the CLI terminal interface) and then based on the captured tool output the system should think and act accordingly to fully accomplish and complete the user tasks and operations with Never Give Up and Retry Logic.

Define the Agent's Behavior:
- System prompt:- with detailed explanations of Core Capabilities, important guidelines, examples, function tool calling usage like how to use them and when to use them and when not to use them and when to use them parallelly or sequential them with very long and well detailed tool description and examples and workflows.

- AI LLM Providers:- Deepseek(Models: deepseek-chat and deepseek-reasoner) and ollama

- LLMs Function Tools calling and executing capabilities:- Send the users query/message to the LLMs and then the LLMs intelegently think and decides if it needs or required to call any available tools and when not to call the tool. Just let the LLMs decides by them self.

Add very well designed and well detailed tool usage discription to all the tools:- Like how to use them, when to use them and when not to use them and tools output Processing and Return tools Result and Usage notes when to use and execute them in parallelly or sequential with very long and well detailed tool description and examples.

bash Tool:- that runs and executes bash commands in the interactive shell. 

grep tool:- Fast content search tool that finds files containing specific text or patterns, returning matching file paths sorted by modification time (newest first).

glob tool:- Fast file pattern matching tool that finds files by name and pattern, returning matching paths sorted by modification time (newest first).

write tool:- File writing tool that creates or updates files in the filesystem, allowing you to save or modify text content.

edit tool:- Edits files by replacing text, creating new files, or deleting content. For moving or renaming files, use the Bash tool with the 'mv' command instead. For larger file edits, use the FileWrite tool to overwrite files.

web Tool:- For reteriving and fetching real time and upto date informations for the internet using free duckduckdo API. 

- Parallelly or sequential LLMs Function Tools calling and executing capabilities
 
Keeping Users Informed:- 
Throughout this entire process, the CLI UI Update system ensures users always know what's happening. It displays AI responses in real-time in the interface, shows when commands are being executed, system presents approval prompts when needed, and provides clear feedback about results or errors. The interface is designed to be informative without being overwhelming. 

Never Give Up: Retry Logic:- 
When temporary issues occur (like network timeouts or rate limits), the Retry Logic automatically attempts to recover. It uses intelligent strategies like exponential backoff for rate limits and distinguishes between temporary issues (worth retrying) and permanent problems (that need user intervention). 

Handling When Things Go Wrong:- 
The Error Handling system monitors every step of the process for potential issues. This includes network problems when communicating with AI providers, command execution failures, permission issues, and unexpected errors. When problems occur, the system provides clear feedback to both the user and the AI about what went wrong. 


 // Custom ball animation including the elapsed seconds
  const ballFrames = [
    "( ●    )",
    "(  ●   )",
    "(   ●  )",
    "(    ● )",
    "(     ●)",
    "(    ● )",
    "(   ●  )",
    "(  ●   )",
    "( ●    )",
    "(●     )",
  ];


Also create a only one single script that provide options to completely install the CLI system globally or update the existing installation or completely uninstall for windows 11 WSL, MacOS and Linux.